# 🔗 Relasi dan Arsitektur Sistem Aplikasi Kesehatan Lansia

## 🏗️ **ARSITEKTUR SISTEM**

### Struktur Aplikasi
```
┌─────────────────────────────────────────────────────────┐
│                    CLIENT (Frontend)                    │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────┐   │
│  │   Next.js   │ │ TypeScript  │ │   TailwindCSS   │   │
│  │   App Router│ │   Type Safe │ │   Modern UI     │   │
│  └─────────────┘ └─────────────┘ └─────────────────┘   │
└─────────────────────────────────────────────────────────┘
                            │
                    ┌───────┴───────┐
                    │   HTTP/API    │
                    │   Requests    │
                    └───────┬───────┘
                            │
┌─────────────────────────────────────────────────────────┐
│                    SERVER (Backend)                     │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────┐   │
│  │  Express.js │ │   Node.js   │ │      CORS       │   │
│  │   REST API  │ │   Runtime   │ │   Middleware    │   │
│  └─────────────┘ └─────────────┘ └─────────────────┘   │
└─────────────────────────────────────────────────────────┘
                            │
                    ┌───────┴───────┐
                    │   MySQL2      │
                    │   Driver      │
                    └───────┬───────┘
                            │
┌─────────────────────────────────────────────────────────┐
│                   DATABASE (MySQL)                      │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────┐   │
│  │   profiles  │ │   checkups  │ │      users      │   │
│  │    Table    │ │    Table    │ │     Table       │   │
│  └─────────────┘ └─────────────┘ └─────────────────┘   │
└─────────────────────────────────────────────────────────┘
```

---

## 📊 **RELASI DATABASE**

### Entity Relationship Diagram (ERD)
```
┌─────────────────┐         ┌─────────────────┐
│      users      │         │    profiles     │
├─────────────────┤         ├─────────────────┤
│ id (PK)         │         │ id (PK)         │
│ username        │         │ nama            │
│ password        │         │ usia            │
│ role            │         │ alamat          │
│ posyandu_name   │         │ riwayat_medis   │
│ pin             │         │ created_at      │
│ is_active       │         │ updated_at      │
│ last_login      │         └─────────────────┘
│ created_at      │                   │
│ updated_at      │                   │ 1:N
└─────────────────┘                   │
                                      ▼
                            ┌─────────────────┐
                            │    checkups     │
                            ├─────────────────┤
                            │ id (PK)         │
                            │ profile_id (FK) │
                            │ tekanan_darah   │
                            │ gula_darah      │
                            │ tanggal         │
                            │ catatan         │
                            │ created_at      │
                            └─────────────────┘
```

### Relasi Tabel
- **profiles → checkups**: One-to-Many (1:N)
  - Satu profil lansia dapat memiliki banyak riwayat pemeriksaan
  - Foreign Key: `checkups.profile_id` → `profiles.id`
  - Cascade Delete: Jika profil dihapus, semua checkup ikut terhapus

- **users**: Independent table untuk autentikasi admin
  - Tidak berelasi langsung dengan profiles/checkups
  - Digunakan untuk kontrol akses sistem

---

## 🔄 **ALUR DATA APLIKASI**

### 1. Pendaftaran Lansia Baru
```
Frontend Form → Validation → API Request → Backend Processing → Database Insert → QR Code Generation → Response
```

**Detail Proses:**
1. **Input Data** (Frontend)
   - Form validation dengan TypeScript
   - Real-time error checking
   - Data sanitization

2. **API Call** (HTTP POST)
   - Endpoint: `/api/profiles`
   - Payload: `{nama, usia, alamat, riwayat_medis, checkup_data}`
   - Headers: Content-Type, Authorization

3. **Backend Processing**
   - Input validation & sanitization
   - Database transaction start
   - Insert ke tabel `profiles`
   - Insert pemeriksaan pertama ke `checkups`
   - Transaction commit/rollback

4. **Response Generation**
   - Generate QR Code dengan profile ID
   - Return profile data + QR Code
   - Error handling jika gagal

### 2. Scanning QR Code
```
QR Scanner → Decode ID → API Request → Database Query → Profile Data → Display
```

**Detail Proses:**
1. **QR Code Scanning**
   - Camera access permission
   - ZXing library untuk decode
   - Extract profile ID dari QR data

2. **Profile Retrieval**
   - API call: `GET /api/profiles/:id`
   - Join query: profiles + checkups
   - Sort checkups by date DESC

3. **Data Display**
   - Profile information
   - Checkup history timeline
   - Health trend visualization

### 3. Pemeriksaan Kesehatan Baru
```
Profile Page → Add Checkup Form → Validation → API Request → Database Insert → Update Display
```

**Detail Proses:**
1. **Form Input**
   - Tekanan darah validation (format: XXX/YY)
   - Gula darah numeric validation
   - Optional catatan text

2. **Data Processing**
   - API call: `POST /api/checkups`
   - Insert ke tabel checkups
   - Update profile.updated_at

3. **UI Update**
   - Refresh checkup history
   - Update health statistics
   - Show success notification

---

## 🌐 **RELASI FRONTEND-BACKEND**

### API Endpoints dan Penggunaan
```
┌─────────────────────┐    ┌─────────────────────┐
│     FRONTEND        │    │      BACKEND        │
│     PAGES           │    │    API ROUTES       │
├─────────────────────┤    ├─────────────────────┤
│ / (Homepage)        │◄──►│ GET /api/health     │
│ /form (Register)    │◄──►│ POST /api/profiles  │
│ /scan (QR Scanner)  │◄──►│ GET /api/profiles/:id│
│ /profile/:id        │◄──►│ POST /api/checkups  │
│ /profiles (List)    │◄──►│ GET /api/profiles   │
│ /admin/*            │◄──►│ POST /api/auth/*    │
└─────────────────────┘    └─────────────────────┘
```

### State Management Flow
```
User Action → Component State → API Call → Backend → Database → Response → State Update → UI Render
```

---

## 🔐 **RELASI KEAMANAN**

### Authentication Flow
```
Login Form → Credential Validation → JWT Token → Protected Routes → API Access
```

**Detail Security:**
1. **Frontend Protection**
   - Protected routes dengan middleware
   - Token storage di localStorage/sessionStorage
   - Auto-redirect jika tidak authenticated

2. **Backend Validation**
   - JWT token verification
   - Role-based access control
   - Rate limiting per endpoint

3. **Database Security**
   - Parameterized queries (SQL injection prevention)
   - Password hashing dengan bcrypt
   - Connection pooling untuk performance

---

## 📱 **RELASI RESPONSIF**

### Device Adaptation
```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   MOBILE    │    │   TABLET    │    │   DESKTOP   │
├─────────────┤    ├─────────────┤    ├─────────────┤
│ QR Scanner  │    │ Form Input  │    │ Full Admin  │
│ Quick View  │    │ Data Entry  │    │ Dashboard   │
│ Essential   │    │ Moderate    │    │ Complete    │
│ Features    │    │ Features    │    │ Features    │
└─────────────┘    └─────────────┘    └─────────────┘
```

### Responsive Breakpoints
- **Mobile**: < 768px - Focus pada scanning dan view
- **Tablet**: 768px - 1024px - Optimal untuk data entry
- **Desktop**: > 1024px - Full administrative features

---

## 🔄 **RELASI REAL-TIME**

### Data Synchronization
```
Database Change → Backend Event → Frontend Update → UI Refresh
```

**Implementasi:**
1. **Polling Strategy**
   - Periodic API calls untuk data terbaru
   - Efficient untuk small-scale deployment

2. **WebSocket (Future)**
   - Real-time updates
   - Multi-user collaboration
   - Live notifications

---

## 📈 **RELASI PERFORMANCE**

### Optimization Strategy
```
Frontend Caching ↔ API Response Caching ↔ Database Indexing
```

**Performance Layers:**
1. **Frontend Optimization**
   - Component lazy loading
   - Image optimization
   - Bundle splitting

2. **API Optimization**
   - Response caching
   - Compression middleware
   - Request batching

3. **Database Optimization**
   - Proper indexing
   - Query optimization
   - Connection pooling

---

## 🔧 **RELASI DEPLOYMENT**

### Production Architecture
```
CDN → Load Balancer → Frontend (Vercel) → Backend (Railway) → Database (PlanetScale)
```

**Deployment Flow:**
1. **Frontend**: Static deployment ke Vercel/Netlify
2. **Backend**: Container deployment ke Railway/Heroku
3. **Database**: Managed MySQL di PlanetScale/Railway
4. **Assets**: CDN untuk static files dan images

---

**💡 Kesimpulan**: Semua komponen sistem saling terintegrasi dengan baik, menggunakan arsitektur modern yang scalable, secure, dan maintainable untuk mendukung operasional posyandu digital yang efisien.
